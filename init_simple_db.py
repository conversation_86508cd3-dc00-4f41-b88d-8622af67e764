#!/usr/bin/env python3
"""
Simple database initialization script for development.
"""
import os
import sqlite3
from datetime import datetime

def create_database():
    """Create SQLite database with basic schema."""
    db_path = "ai_dashboard.db"
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database: {db_path}")
    
    # Create new database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create content table
    cursor.execute('''
        CREATE TABLE content (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT,
            url TEXT,
            source TEXT NOT NULL,
            category TEXT,
            published_date DATETIME,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            summary TEXT
        )
    ''')
    
    # Insert some sample data
    sample_data = [
        (
            "OpenAI Releases GPT-4 Turbo",
            "OpenAI has announced the release of GPT-4 Turbo with improved capabilities...",
            "https://openai.com/blog/gpt-4-turbo",
            "openai_blog",
            "Product Releases",
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            "OpenAI announces GPT-4 Turbo with enhanced performance and reduced costs."
        ),
        (
            "AI Research Breakthrough in Computer Vision",
            "Researchers at MIT have developed a new approach to computer vision...",
            "https://news.mit.edu/ai-vision",
            "mit_ai",
            "Research",
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            "MIT researchers achieve breakthrough in computer vision technology."
        ),
        (
            "The Future of AI in Healthcare",
            "Industry experts discuss the potential applications of AI in medical diagnosis...",
            "https://wired.com/ai-healthcare",
            "wired_ai",
            "Opinion Pieces",
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            "Experts explore AI's transformative potential in healthcare and medical diagnosis."
        )
    ]
    
    cursor.executemany('''
        INSERT INTO content (title, content, url, source, category, published_date, created_date, summary)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', sample_data)
    
    conn.commit()
    conn.close()
    
    print(f"✅ Database created successfully: {db_path}")
    print(f"✅ Inserted {len(sample_data)} sample records")

if __name__ == "__main__":
    create_database()
