#!/usr/bin/env python3
"""
Simple API script to fetch content from SQLite database.
This replaces the complex backend/api/get_content.py for development.
"""
import sqlite3
import json
import sys
import argparse
from datetime import datetime

def get_content(date=None, source=None, limit=50):
    """
    Fetch content from SQLite database.
    
    Args:
        date: Filter by date (YYYY-MM-DD format)
        source: Filter by source
        limit: Maximum number of results
    
    Returns:
        dict: Content data in JSON format
    """
    try:
        conn = sqlite3.connect('ai_dashboard.db')
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()
        
        # Build query
        query = "SELECT * FROM content WHERE 1=1"
        params = []
        
        if date:
            query += " AND DATE(published_date) = ?"
            params.append(date)
        
        if source:
            query += " AND source = ?"
            params.append(source)
        
        query += " ORDER BY published_date DESC"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Convert to list of dictionaries
        content = []
        for row in rows:
            content.append({
                'id': row['id'],
                'title': row['title'],
                'content': row['content'],
                'url': row['url'],
                'source': row['source'],
                'category': row['category'],
                'published_date': row['published_date'],
                'created_date': row['created_date'],
                'summary': row['summary']
            })
        
        conn.close()
        
        result = {
            'content': content,
            'total': len(content),
            'filters': {
                'date': date,
                'source': source,
                'limit': limit
            }
        }
        
        return result
        
    except Exception as e:
        return {
            'error': str(e),
            'content': [],
            'total': 0
        }

def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description='Fetch content from database')
    parser.add_argument('--date', help='Filter by date (YYYY-MM-DD)')
    parser.add_argument('--source', help='Filter by source')
    parser.add_argument('--limit', type=int, default=50, help='Maximum number of results')
    
    args = parser.parse_args()
    
    result = get_content(
        date=args.date,
        source=args.source,
        limit=args.limit
    )
    
    # Output JSON to stdout
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
